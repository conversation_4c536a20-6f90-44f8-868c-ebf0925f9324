import { inject } from "vue";
import { ToastItem } from "./toast.vue";

export const ToastSymbol = Symbol('Toast');

export interface IToastHookReturn {
  show(options: ToastOptions): string;
  success(message: string, options?: Partial<ToastOptions>): string;
  error(message: string, options?: Partial<ToastOptions>): string;
  warning(message: string, options?: Partial<ToastOptions>): string;
  info(message: string, options?: Partial<ToastOptions>): string;
  hide(id: string): void;
  clear(): void;
}

export interface ToastOptions {
  id?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number; // 自动消失时间，0 表示不自动消失
  closable?: boolean; // 是否显示关闭按钮
  icon?: any; // Vue 组件
}

export default function useToast() {
  const toastInstance = inject(ToastSymbol);
  if (!toastInstance) {
    console.error('useToast must be used within a ToastProvider');
    throw new Error('useToast must be used within a ToastProvider');
  }
  return toastInstance as IToastHookReturn;
}
